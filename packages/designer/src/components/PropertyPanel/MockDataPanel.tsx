import React, { useState } from 'react';
import { useDesigner } from '../../context/DesignerContext';
import { findComponent } from '@lowcode/shared';

export interface MockDataPanelProps {
  width?: number;
}

export const MockDataPanel: React.FC<MockDataPanelProps> = ({ width: _width = 576 }) => {
  const { schema, canvasState, updateComponent } = useDesigner();
  const [activeApiIndex, setActiveApiIndex] = useState(0);

  // 获取当前选中的组件
  const selectedComponent = canvasState.selectedComponentId 
    ? findComponent(schema.components, canvasState.selectedComponentId)
    : null;

  if (!selectedComponent) {
    return (
      <div style={{ 
        padding: '32px', 
        textAlign: 'center', 
        color: '#999' 
      }}>
        请先选择一个组件
      </div>
    );
  }

  // 解析组件的模拟数据，支持多个接口
  let mockDataConfig: { apis: Array<{ name: string; data: string }> } = { apis: [] };
  
  try {
    if (selectedComponent.mockData) {
      const parsed = JSON.parse(selectedComponent.mockData);
      if (Array.isArray(parsed)) {
        // 兼容旧格式：直接是数组数据
        mockDataConfig = {
          apis: [{ name: '默认接口', data: JSON.stringify(parsed, null, 2) }]
        };
      } else if (parsed.apis && Array.isArray(parsed.apis)) {
        // 新格式：包含多个接口
        mockDataConfig = parsed;
      } else {
        // 其他格式：作为单个接口数据
        mockDataConfig = {
          apis: [{ name: '默认接口', data: JSON.stringify(parsed, null, 2) }]
        };
      }
    }
  } catch (error) {
    console.warn('Failed to parse mockData:', error);
  }

  // 如果没有接口数据，提供默认数据
  if (mockDataConfig.apis.length === 0) {
    if (selectedComponent.type === 'TableViewWithSearch') {
      mockDataConfig = {
        apis: [{
          name: 'API列表接口',
          data: JSON.stringify([
            { id: 1, path: '/login', sensitivity: '高敏感', riskLevel: '高风险', totalVisits: '3.6千', trafficSource: '192.168.0.1', firstSeen: '2025-08-14 19:18:21' },
            { id: 2, path: '/abnormal', sensitivity: '高敏感', riskLevel: '高风险', totalVisits: '2.5千', trafficSource: '192.168.0.1', firstSeen: '2025-08-14 19:19:12' },
            { id: 3, path: '/api/users', sensitivity: '中敏感', riskLevel: '中风险', totalVisits: '1.2千', trafficSource: '192.168.0.2', firstSeen: '2025-08-14 20:15:33' }
          ], null, 2)
        }]
      };
    } else if (selectedComponent.type === 'SidebarTreeView') {
      mockDataConfig = {
        apis: [{
          name: '视图列表接口',
          data: JSON.stringify([
            { key: 'all', title: '全部API', count: 212, href: '/apis/all' },
            {
              key: 'penetration',
              title: '渗透测试重点API',
              children: [
                { key: 'login', title: '登录API', count: 26 },
                { key: 'url', title: 'URL重定向API', count: 5 },
                { key: 'response', title: '单次响应数据量过大...', count: 3 },
                { key: 'sms', title: '短信验证码发送API', count: 2 },
                { key: 'register', title: '注册API', count: 0 }
              ]
            },
            { key: 'network', title: '互联网敏感API' },
            { key: 'format', title: 'API格式' },
            { key: 'label', title: 'API标签' }
          ], null, 2)
        }]
      };
    } else {
      mockDataConfig = {
        apis: [{
          name: '默认接口',
          data: JSON.stringify({ message: '请配置接口数据' }, null, 2)
        }]
      };
    }
    
    // 保存默认数据
    updateComponent(selectedComponent.id, { mockData: JSON.stringify(mockDataConfig) });
  }

  const currentApi = mockDataConfig.apis[activeApiIndex] || mockDataConfig.apis[0];

  const handleUpdateApiData = (data: string) => {
    const newConfig = { ...mockDataConfig };
    newConfig.apis[activeApiIndex].data = data;
    updateComponent(selectedComponent.id, { mockData: JSON.stringify(newConfig) });
  };

  const handleUpdateApiName = (name: string) => {
    const newConfig = { ...mockDataConfig };
    newConfig.apis[activeApiIndex].name = name;
    updateComponent(selectedComponent.id, { mockData: JSON.stringify(newConfig) });
  };

  const handleAddApi = () => {
    const newConfig = { ...mockDataConfig };
    newConfig.apis.push({
      name: `接口${newConfig.apis.length + 1}`,
      data: JSON.stringify({ message: '新接口数据' }, null, 2)
    });
    updateComponent(selectedComponent.id, { mockData: JSON.stringify(newConfig) });
    setActiveApiIndex(newConfig.apis.length - 1);
  };

  const handleDeleteApi = () => {
    if (mockDataConfig.apis.length <= 1) {
      alert('至少需要保留一个接口');
      return;
    }
    
    const newConfig = { ...mockDataConfig };
    newConfig.apis.splice(activeApiIndex, 1);
    updateComponent(selectedComponent.id, { mockData: JSON.stringify(newConfig) });
    setActiveApiIndex(Math.max(0, activeApiIndex - 1));
  };

  return (
    <div style={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: '#ffffff'
    }}>
      {/* 标题栏 */}
      <div style={{
        padding: '16px 20px',
        borderBottom: '1px solid #e8e8e8',
        backgroundColor: '#ffffff'
      }}>
        <div style={{ fontSize: 14, fontWeight: 600, color: '#262626', marginBottom: 4 }}>
          模拟数据
        </div>
        <div style={{ fontSize: 12, color: '#8c8c8c' }}>
          {selectedComponent.type} (ID: {selectedComponent.id})
        </div>
      </div>

      {/* 接口标签页 */}
      <div style={{
        padding: '12px 20px',
        borderBottom: '1px solid #e8e8e8',
        backgroundColor: '#fafafa',
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}>
        {mockDataConfig.apis.map((api, index) => (
          <div
            key={index}
            onClick={() => setActiveApiIndex(index)}
            style={{
              padding: '6px 12px',
              backgroundColor: activeApiIndex === index ? '#1890ff' : '#ffffff',
              color: activeApiIndex === index ? '#ffffff' : '#666',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '4px'
            }}
          >
            {api.name}
            {mockDataConfig.apis.length > 1 && (
              <span
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteApi();
                }}
                style={{
                  marginLeft: '4px',
                  fontSize: '14px',
                  opacity: 0.7,
                  cursor: 'pointer'
                }}
              >
                ×
              </span>
            )}
          </div>
        ))}
        
        <button
          onClick={handleAddApi}
          style={{
            padding: '6px 8px',
            backgroundColor: '#ffffff',
            color: '#666',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          + 添加接口
        </button>
      </div>

      {/* 接口名称编辑 */}
      <div style={{ padding: '12px 20px', borderBottom: '1px solid #e8e8e8' }}>
        <label style={{ 
          display: 'block',
          marginBottom: '6px',
          fontSize: '12px', 
          fontWeight: '500',
          color: '#333'
        }}>
          接口名称
        </label>
        <input
          type="text"
          value={currentApi?.name || ''}
          onChange={(e) => handleUpdateApiName(e.target.value)}
          style={{
            width: '100%',
            padding: '6px 10px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            fontSize: '12px',
            outline: 'none'
          }}
        />
      </div>

      {/* JSON数据编辑器 */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', padding: '16px 20px' }}>
        <label style={{ 
          display: 'block',
          marginBottom: '8px',
          fontSize: '12px', 
          fontWeight: '500',
          color: '#333'
        }}>
          JSON数据
        </label>
        
        <textarea
          value={currentApi?.data || ''}
          onChange={(e) => handleUpdateApiData(e.target.value)}
          placeholder="请输入JSON格式的接口返回数据"
          style={{
            flex: 1,
            padding: '12px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            fontSize: '12px',
            fontFamily: 'Monaco, Consolas, "Courier New", monospace',
            lineHeight: '1.5',
            resize: 'none',
            outline: 'none',
            backgroundColor: '#ffffff'
          }}
          onFocus={(e) => {
            e.currentTarget.style.borderColor = '#1890ff';
            e.currentTarget.style.boxShadow = '0 0 0 2px rgba(24, 144, 255, 0.2)';
          }}
          onBlur={(e) => {
            e.currentTarget.style.borderColor = '#d9d9d9';
            e.currentTarget.style.boxShadow = 'none';
          }}
        />
      </div>
    </div>
  );
};
