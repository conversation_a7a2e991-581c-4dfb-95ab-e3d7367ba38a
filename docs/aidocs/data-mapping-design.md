# 低代码平台数据映射设计方案

## 背景与挑战

在低代码平台中，组件显示的数据来源于API接口，但API返回的原始数据格式往往与组件期望的数据格式不匹配，需要一层映射关系来转换数据。

### 核心挑战
1. **数据格式差异**：API返回的字段名、数据结构与组件期望的不一致
2. **业务差异**：不同业务系统的表格字段、视图结构各不相同
3. **配置复杂度**：如何在保持灵活性的同时，最小化用户配置负担
4. **类型安全**：确保映射过程的数据类型正确性

## 设计理念

### 1. 分层设计
- **固定映射层**：对于格式相对固定的组件（如SidebarTreeView）
- **可配置映射层**：对于字段灵活的组件（如TableViewWithSearch）
- **智能推断层**：基于数据结构自动推断映射关系

### 2. 最小配置原则
- 优先使用约定大于配置
- 提供智能默认映射
- 只在必要时要求用户配置

### 3. 渐进式复杂度
- 简单场景零配置
- 复杂场景提供精细控制
- 支持从简单到复杂的平滑过渡

## 技术方案

### 1. 数据映射架构

```typescript
// 数据映射配置接口
interface DataMappingConfig {
  // 数据源配置
  source: {
    apiId: string;           // API接口ID
    dataPath?: string;       // 数据提取路径，如 'data.list'
    mockData?: string;       // 模拟数据（开发阶段）
  };
  
  // 映射规则
  mapping: {
    mode: 'auto' | 'manual' | 'template';  // 映射模式
    rules?: MappingRule[];                  // 手动映射规则
    template?: string;                      // 预定义模板
  };
  
  // 数据转换
  transform?: {
    filters?: FilterRule[];     // 数据过滤
    sorting?: SortRule[];       // 数据排序
    pagination?: PaginationConfig; // 分页配置
  };
}

// 映射规则
interface MappingRule {
  source: string;      // 源字段路径
  target: string;      // 目标字段名
  type?: DataType;     // 数据类型
  transform?: string;  // 转换函数名
  default?: any;       // 默认值
}
```

### 2. 组件级别的映射策略

#### A. SidebarTreeView（固定映射）
```typescript
// 固定的数据格式约定
interface TreeNodeData {
  key: string;
  title: string;
  icon?: string;
  href?: string;
  children?: TreeNodeData[];
  count?: number;
}

// 内置映射模板
const TREE_VIEW_MAPPING_TEMPLATE = {
  mode: 'template',
  template: 'tree-view',
  rules: [
    { source: 'id', target: 'key' },
    { source: 'name', target: 'title' },
    { source: 'iconUrl', target: 'icon' },
    { source: 'link', target: 'href' },
    { source: 'subItems', target: 'children' },
    { source: 'itemCount', target: 'count' }
  ]
};
```

#### B. TableViewWithSearch（灵活映射）
```typescript
// 表格列配置
interface TableColumnMapping {
  key: string;           // 列标识
  title: string;         // 列标题
  dataIndex: string;     // 数据字段路径
  sourceField: string;   // API源字段
  type: 'text' | 'number' | 'date' | 'enum' | 'boolean';
  format?: string;       // 格式化规则
  sortable?: boolean;    // 是否可排序
  filterable?: boolean;  // 是否可筛选
}

// 智能列映射配置
interface SmartTableMapping {
  mode: 'smart' | 'manual';
  autoDetect: boolean;           // 是否自动检测字段
  columnMappings: TableColumnMapping[];
  excludeFields?: string[];      // 排除的字段
  includeFields?: string[];      // 包含的字段
}
```

### 3. 映射模式详解

#### 模式1：自动映射（Auto）
```json
{
  "mapping": {
    "mode": "auto",
    "autoDetect": true
  }
}
```
- 系统自动分析API返回数据结构
- 基于字段名智能匹配组件期望格式
- 适用于字段名规范的标准API

#### 模式2：模板映射（Template）
```json
{
  "mapping": {
    "mode": "template",
    "template": "standard-table",
    "customizations": {
      "titleField": "displayName",
      "statusField": "currentStatus"
    }
  }
}
```
- 使用预定义的映射模板
- 支持少量自定义覆盖
- 适用于常见的业务场景

#### 模式3：手动映射（Manual）
```json
{
  "mapping": {
    "mode": "manual",
    "rules": [
      {
        "source": "api_path",
        "target": "path",
        "type": "string"
      },
      {
        "source": "risk_score",
        "target": "riskLevel",
        "type": "string",
        "transform": "riskScoreToLevel"
      }
    ]
  }
}
```
- 完全自定义映射规则
- 支持复杂的数据转换
- 适用于特殊业务需求

## 用户配置界面设计

### 1. 数据源配置
```
┌─ 数据源配置 ─────────────────────────┐
│ API接口: [选择接口 ▼] [测试连接]      │
│ 数据路径: data.result.list           │
│ ┌─ 预览数据 ─────────────────────┐   │
│ │ {                              │   │
│ │   "id": "api_001",             │   │
│ │   "name": "用户登录API",        │   │
│ │   "path": "/api/user/login"    │   │
│ │ }                              │   │
│ └────────────────────────────────┘   │
└─────────────────────────────────────┘
```

### 2. 智能映射配置
```
┌─ 字段映射 ───────────────────────────┐
│ 映射模式: ○ 自动 ● 模板 ○ 手动      │
│ 模板选择: [标准表格 ▼]               │
│                                      │
│ ┌─ 字段映射预览 ─────────────────┐   │
│ │ API字段    →  表格列           │   │
│ │ id         →  key             │   │
│ │ name       →  title           │   │
│ │ api_path   →  path            │   │
│ │ risk_score →  riskLevel       │   │
│ └────────────────────────────────┘   │
│ [自定义映射] [添加转换规则]          │
└─────────────────────────────────────┘
```

### 3. 预览与验证
```
┌─ 映射结果预览 ───────────────────────┐
│ ┌─ 原始数据 ─┐  ┌─ 映射后数据 ─┐   │
│ │ {          │  │ {            │   │
│ │   "id": 1, │→ │   "key": 1,  │   │
│ │   "name":  │  │   "title":   │   │
│ │   "登录API" │  │   "登录API"   │   │
│ │ }          │  │ }            │   │
│ └────────────┘  └──────────────┘   │
│ [应用映射] [保存配置]                │
└─────────────────────────────────────┘
```

## 实现策略

### 阶段1：基础映射（当前实现）
- [x] 模拟数据支持
- [x] 固定格式组件（SidebarTreeView）
- [ ] 基础的手动映射配置

### 阶段2：智能映射
- [ ] 自动字段检测
- [ ] 预定义映射模板
- [ ] 映射配置UI

### 阶段3：高级功能
- [ ] 数据转换函数
- [ ] 复杂嵌套映射
- [ ] 实时数据同步

## 配置示例

### 示例1：SidebarTreeView（零配置）
```json
{
  "id": "sidebar",
  "type": "SidebarTreeView",
  "dataSource": {
    "apiId": "getMenuTree",
    "mapping": {
      "mode": "template",
      "template": "tree-view"
    }
  }
}
```

### 示例2：TableViewWithSearch（智能配置）
```json
{
  "id": "api_table",
  "type": "TableViewWithSearch",
  "dataSource": {
    "apiId": "getApiList",
    "mapping": {
      "mode": "smart",
      "autoDetect": true,
      "columnMappings": [
        {
          "key": "path",
          "title": "API路径",
          "sourceField": "api_path",
          "type": "text"
        },
        {
          "key": "riskLevel",
          "title": "风险等级",
          "sourceField": "risk_score",
          "type": "enum",
          "transform": "riskScoreToLevel"
        }
      ]
    }
  }
}
```

## 技术实现细节

### 1. 数据映射引擎
```typescript
class DataMappingEngine {
  // 执行数据映射
  static transform(data: any, config: DataMappingConfig): any {
    switch (config.mapping.mode) {
      case 'auto':
        return this.autoMapping(data, config);
      case 'template':
        return this.templateMapping(data, config);
      case 'manual':
        return this.manualMapping(data, config);
    }
  }

  // 自动映射：基于字段名智能匹配
  private static autoMapping(data: any, config: DataMappingConfig): any {
    const fieldMappings = this.detectFieldMappings(data);
    return this.applyMappings(data, fieldMappings);
  }

  // 模板映射：使用预定义模板
  private static templateMapping(data: any, config: DataMappingConfig): any {
    const template = this.getTemplate(config.mapping.template);
    const customizations = config.mapping.customizations || {};
    const finalRules = { ...template.rules, ...customizations };
    return this.applyMappings(data, finalRules);
  }

  // 手动映射：按用户配置的规则
  private static manualMapping(data: any, config: DataMappingConfig): any {
    return this.applyMappings(data, config.mapping.rules);
  }
}
```

### 2. 预定义映射模板
```typescript
// 树形视图模板
const TREE_VIEW_TEMPLATE = {
  name: 'tree-view',
  description: '树形视图数据映射',
  rules: [
    { source: 'id|key|_id', target: 'key', type: 'string' },
    { source: 'name|title|label', target: 'title', type: 'string' },
    { source: 'icon|iconUrl', target: 'icon', type: 'string' },
    { source: 'url|href|link', target: 'href', type: 'string' },
    { source: 'children|subItems|items', target: 'children', type: 'array' },
    { source: 'count|total|num', target: 'count', type: 'number' }
  ]
};

// 标准表格模板
const STANDARD_TABLE_TEMPLATE = {
  name: 'standard-table',
  description: '标准表格数据映射',
  autoColumns: true,
  commonMappings: {
    'id|_id|key': 'key',
    'name|title|label': 'title',
    'status|state': 'status',
    'createTime|created_at|createdAt': 'createTime',
    'updateTime|updated_at|updatedAt': 'updateTime'
  }
};
```

### 3. 智能字段检测
```typescript
class FieldDetector {
  // 分析数据结构，推断字段映射
  static analyzeDataStructure(data: any[]): FieldAnalysis {
    if (!data || data.length === 0) return { fields: [], suggestions: [] };

    const sample = data[0];
    const fields = Object.keys(sample).map(key => ({
      name: key,
      type: this.inferType(sample[key]),
      samples: data.slice(0, 3).map(item => item[key])
    }));

    const suggestions = this.generateMappingSuggestions(fields);

    return { fields, suggestions };
  }

  // 生成映射建议
  private static generateMappingSuggestions(fields: FieldInfo[]): MappingSuggestion[] {
    return fields.map(field => ({
      sourceField: field.name,
      suggestedTarget: this.suggestTargetField(field.name),
      confidence: this.calculateConfidence(field.name),
      reason: this.explainSuggestion(field.name)
    }));
  }
}
```

### 4. 配置界面组件
```typescript
// 数据映射配置组件
const DataMappingConfig: React.FC<{
  componentId: string;
  onConfigChange: (config: DataMappingConfig) => void;
}> = ({ componentId, onConfigChange }) => {
  const [mappingMode, setMappingMode] = useState<'auto' | 'template' | 'manual'>('auto');
  const [previewData, setPreviewData] = useState(null);

  return (
    <div className="data-mapping-config">
      <div className="mapping-mode-selector">
        <Radio.Group value={mappingMode} onChange={e => setMappingMode(e.target.value)}>
          <Radio value="auto">智能映射</Radio>
          <Radio value="template">模板映射</Radio>
          <Radio value="manual">手动映射</Radio>
        </Radio.Group>
      </div>

      {mappingMode === 'template' && (
        <TemplateSelector onTemplateChange={handleTemplateChange} />
      )}

      {mappingMode === 'manual' && (
        <ManualMappingEditor rules={mappingRules} onChange={handleRulesChange} />
      )}

      <DataPreview original={originalData} mapped={mappedData} />
    </div>
  );
};
```

## 用户使用流程

### 流程1：零配置使用（SidebarTreeView）
1. 拖拽SidebarTreeView组件到画布
2. 在MockData面板配置树形数据
3. 组件自动使用内置映射模板
4. 立即可用，无需额外配置

### 流程2：智能配置使用（TableViewWithSearch）
1. 拖拽TableViewWithSearch组件到画布
2. 选择API接口或配置模拟数据
3. 系统自动分析数据结构，生成列配置
4. 用户可选择性调整列标题、类型等
5. 预览效果，确认后应用

### 流程3：高级自定义使用
1. 选择手动映射模式
2. 配置详细的字段映射规则
3. 设置数据转换函数
4. 实时预览映射效果
5. 保存配置

## 最佳实践建议

### 1. 组件设计原则
- **固定格式组件**：内置映射模板，零配置使用
- **灵活组件**：提供智能检测 + 可选自定义
- **专业组件**：支持完整的手动配置能力

### 2. API设计规范
- 推荐使用标准的字段命名约定
- 提供清晰的数据结构文档
- 支持数据格式版本控制

### 3. 用户体验优化
- 提供丰富的映射模板库
- 实时预览映射效果
- 智能错误提示和修复建议
- 支持配置的导入导出

## 总结

这个数据映射方案通过分层设计和渐进式复杂度，在保持灵活性的同时最大化降低了用户配置负担：

1. **简单场景**：SidebarTreeView等固定格式组件零配置使用
2. **标准场景**：使用预定义模板，少量自定义
3. **复杂场景**：提供完整的手动映射能力

核心优势：
- 🎯 **用户友好**：大部分场景下接近零配置
- 🔧 **开发友好**：清晰的数据流和类型安全
- 🚀 **扩展性强**：支持从简单到复杂的平滑演进
- 📊 **可视化**：提供直观的映射配置界面
- 🤖 **智能化**：自动检测和推荐映射关系
