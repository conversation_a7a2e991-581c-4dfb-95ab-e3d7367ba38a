# 数据映射实现指南

## 当前实现状态

### 已实现功能
- ✅ 模拟数据系统（MockData）
- ✅ ComponentDataContext数据传递
- ✅ SidebarTreeView固定格式映射
- ✅ TableViewWithSearch基础数据绑定

### 待实现功能
- 🔄 智能字段检测
- 🔄 映射配置UI
- 🔄 预定义模板系统
- 🔄 数据转换引擎

## 实现路径

### 第一阶段：扩展现有MockData系统

#### 1. 增强MockData格式
```typescript
// 当前格式
interface MockDataConfig {
  apis: Array<{
    name: string;
    data: string; // JSON字符串
  }>;
}

// 扩展格式
interface EnhancedMockDataConfig {
  apis: Array<{
    name: string;
    data: string;
    mapping?: DataMappingConfig; // 新增映射配置
    schema?: DataSchema;         // 数据结构描述
  }>;
}
```

#### 2. 修改useMockData Hook
```typescript
// packages/shared/src/context/ComponentDataContext.tsx
export const useMockData = (
  componentId?: string, 
  apiName?: string,
  mappingConfig?: DataMappingConfig
): any[] => {
  const { getComponentData } = useComponentData();

  return React.useMemo(() => {
    if (!componentId) return [];

    const componentData = getComponentData(componentId);
    if (!componentData?.mockData) return [];

    try {
      const mockData = JSON.parse(componentData.mockData);
      
      // 获取原始数据
      let rawData = [];
      if (mockData.apis && Array.isArray(mockData.apis)) {
        const api = apiName 
          ? mockData.apis.find((api: any) => api.name === apiName)
          : mockData.apis[0];
        
        if (api) {
          rawData = JSON.parse(api.data);
          
          // 应用数据映射
          if (api.mapping || mappingConfig) {
            const config = api.mapping || mappingConfig;
            rawData = DataMappingEngine.transform(rawData, config);
          }
        }
      } else {
        rawData = Array.isArray(mockData) ? mockData : [mockData];
      }

      return rawData;
    } catch (error) {
      console.warn('Failed to parse mockData:', error);
      return [];
    }
  }, [componentId, apiName, mappingConfig, getComponentData]);
};
```

### 第二阶段：实现数据映射引擎

#### 1. 创建映射引擎核心
```typescript
// packages/shared/src/utils/dataMappingEngine.ts
export class DataMappingEngine {
  private static templates: Map<string, MappingTemplate> = new Map();

  // 注册映射模板
  static registerTemplate(template: MappingTemplate) {
    this.templates.set(template.name, template);
  }

  // 执行数据映射
  static transform(data: any, config: DataMappingConfig): any {
    if (!data || !config) return data;

    switch (config.mapping.mode) {
      case 'auto':
        return this.autoMapping(data, config);
      case 'template':
        return this.templateMapping(data, config);
      case 'manual':
        return this.manualMapping(data, config);
      default:
        return data;
    }
  }

  // 自动映射实现
  private static autoMapping(data: any, config: DataMappingConfig): any {
    if (!Array.isArray(data) || data.length === 0) return data;

    const sample = data[0];
    const detectedMappings = this.detectFieldMappings(sample);
    
    return data.map(item => this.applyMappings(item, detectedMappings));
  }

  // 字段映射检测
  private static detectFieldMappings(sample: any): MappingRule[] {
    const rules: MappingRule[] = [];
    
    Object.keys(sample).forEach(key => {
      const suggestedTarget = this.suggestTargetField(key);
      if (suggestedTarget) {
        rules.push({
          source: key,
          target: suggestedTarget,
          type: this.inferType(sample[key])
        });
      }
    });

    return rules;
  }

  // 字段名建议
  private static suggestTargetField(sourceField: string): string | null {
    const mappings = {
      // 通用字段映射
      'id': 'key',
      '_id': 'key',
      'uuid': 'key',
      'name': 'title',
      'title': 'title',
      'label': 'title',
      'displayName': 'title',
      
      // 时间字段映射
      'createTime': 'createTime',
      'created_at': 'createTime',
      'createdAt': 'createTime',
      'updateTime': 'updateTime',
      'updated_at': 'updateTime',
      'updatedAt': 'updateTime',
      
      // 状态字段映射
      'status': 'status',
      'state': 'status',
      'enabled': 'status',
      'active': 'status'
    };

    // 精确匹配
    if (mappings[sourceField]) {
      return mappings[sourceField];
    }

    // 模糊匹配
    const lowerField = sourceField.toLowerCase();
    for (const [pattern, target] of Object.entries(mappings)) {
      if (lowerField.includes(pattern.toLowerCase())) {
        return target;
      }
    }

    return null;
  }

  // 类型推断
  private static inferType(value: any): DataType {
    if (value === null || value === undefined) return 'string';
    if (typeof value === 'boolean') return 'boolean';
    if (typeof value === 'number') return 'number';
    if (Array.isArray(value)) return 'array';
    if (typeof value === 'object') return 'object';
    
    // 字符串类型的进一步判断
    if (typeof value === 'string') {
      // 日期格式检测
      if (/^\d{4}-\d{2}-\d{2}/.test(value)) return 'date';
      // URL格式检测
      if (/^https?:\/\//.test(value)) return 'url';
      // 邮箱格式检测
      if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'email';
    }
    
    return 'string';
  }
}
```

#### 2. 预定义映射模板
```typescript
// packages/shared/src/utils/mappingTemplates.ts

// 树形视图模板
export const TREE_VIEW_TEMPLATE: MappingTemplate = {
  name: 'tree-view',
  description: '树形视图数据映射模板',
  targetSchema: {
    key: 'string',
    title: 'string',
    icon: 'string?',
    href: 'string?',
    children: 'array?',
    count: 'number?'
  },
  rules: [
    { source: 'id', target: 'key', type: 'string' },
    { source: 'name', target: 'title', type: 'string' },
    { source: 'icon', target: 'icon', type: 'string' },
    { source: 'url', target: 'href', type: 'string' },
    { source: 'children', target: 'children', type: 'array' },
    { source: 'count', target: 'count', type: 'number' }
  ],
  fallbackRules: [
    { source: ['_id', 'uuid'], target: 'key' },
    { source: ['title', 'label', 'displayName'], target: 'title' },
    { source: ['iconUrl', 'iconPath'], target: 'icon' },
    { source: ['link', 'href', 'path'], target: 'href' },
    { source: ['subItems', 'items'], target: 'children' },
    { source: ['total', 'num'], target: 'count' }
  ]
};

// 标准表格模板
export const STANDARD_TABLE_TEMPLATE: MappingTemplate = {
  name: 'standard-table',
  description: '标准表格数据映射模板',
  autoDetectColumns: true,
  commonMappings: {
    'id': { target: 'key', type: 'string', title: 'ID' },
    'name': { target: 'name', type: 'string', title: '名称' },
    'status': { target: 'status', type: 'enum', title: '状态' },
    'createTime': { target: 'createTime', type: 'date', title: '创建时间' }
  },
  columnDefaults: {
    sortable: true,
    filterable: false,
    width: 120
  }
};

// 注册默认模板
DataMappingEngine.registerTemplate(TREE_VIEW_TEMPLATE);
DataMappingEngine.registerTemplate(STANDARD_TABLE_TEMPLATE);
```

### 第三阶段：组件集成

#### 1. 更新SidebarTreeView
```typescript
// packages/shared/src/components/business/SidebarTreeView.tsx
export const SidebarTreeView: React.FC<SidebarTreeViewProps> = ({
  // ... 其他props
}) => {
  // 使用内置的树形视图映射模板
  const mappingConfig: DataMappingConfig = {
    source: { apiId: 'treeData' },
    mapping: {
      mode: 'template',
      template: 'tree-view'
    }
  };

  const mockData = useMockData(componentId, undefined, mappingConfig);
  
  // ... 其余组件逻辑
};
```

#### 2. 更新TableViewWithSearch
```typescript
// packages/shared/src/components/business/TableViewWithSearch.tsx
export const TableViewWithSearch: React.FC<TableViewWithSearchProps> = ({
  // ... 其他props
}) => {
  // 支持自定义映射配置
  const [mappingConfig, setMappingConfig] = useState<DataMappingConfig>({
    source: { apiId: 'tableData' },
    mapping: {
      mode: 'auto' // 默认使用自动映射
    }
  });

  const mockData = useMockData(componentId, undefined, mappingConfig);
  
  // ... 其余组件逻辑
};
```

## 配置界面实现

### 1. 映射配置面板
```typescript
// packages/designer/src/components/PropertyPanel/DataMappingPanel.tsx
export const DataMappingPanel: React.FC<{
  componentId: string;
  currentConfig?: DataMappingConfig;
  onChange: (config: DataMappingConfig) => void;
}> = ({ componentId, currentConfig, onChange }) => {
  const [mode, setMode] = useState(currentConfig?.mapping.mode || 'auto');
  const [previewData, setPreviewData] = useState(null);

  return (
    <div className="data-mapping-panel">
      <div className="mapping-mode">
        <label>映射模式</label>
        <Select value={mode} onChange={setMode}>
          <Option value="auto">智能映射</Option>
          <Option value="template">模板映射</Option>
          <Option value="manual">手动映射</Option>
        </Select>
      </div>

      {mode === 'template' && (
        <TemplateSelector 
          value={currentConfig?.mapping.template}
          onChange={handleTemplateChange}
        />
      )}

      {mode === 'manual' && (
        <ManualMappingEditor
          rules={currentConfig?.mapping.rules || []}
          onChange={handleRulesChange}
        />
      )}

      <DataPreview
        originalData={previewData?.original}
        mappedData={previewData?.mapped}
      />
    </div>
  );
};
```

## 使用示例

### 示例1：API管理表格的数据映射
```json
{
  "id": "api_table",
  "type": "TableViewWithSearch",
  "mockData": {
    "apis": [{
      "name": "getApiList",
      "data": "[{\"api_id\":\"001\",\"api_name\":\"用户登录\",\"api_path\":\"/login\",\"risk_score\":85}]",
      "mapping": {
        "mode": "manual",
        "rules": [
          {"source": "api_id", "target": "key", "type": "string"},
          {"source": "api_name", "target": "name", "type": "string"},
          {"source": "api_path", "target": "path", "type": "string"},
          {"source": "risk_score", "target": "riskLevel", "type": "string", "transform": "scoreToLevel"}
        ]
      }
    }]
  }
}
```

### 示例2：视图树的自动映射
```json
{
  "id": "sidebar",
  "type": "SidebarTreeView",
  "mockData": {
    "apis": [{
      "name": "getMenuTree",
      "data": "[{\"id\":\"all\",\"name\":\"全部API\",\"count\":212}]",
      "mapping": {
        "mode": "template",
        "template": "tree-view"
      }
    }]
  }
}
```

## 下一步计划

1. **实现数据映射引擎核心功能**
2. **创建映射配置UI组件**
3. **集成到现有的MockData面板**
4. **添加更多预定义模板**
5. **支持数据转换函数**
6. **实现实时API数据映射**

这个实现方案保持了我们"最小配置"的设计理念，同时为复杂场景提供了足够的灵活性。
